#!/usr/bin/env python3
"""
模型下载脚本 - 下载 gte-large 嵌入模型到本地
"""

import os
import sys
from pathlib import Path
from typing import Optional

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from transformers import AutoTokenizer, AutoModel
    from sentence_transformers import SentenceTransformer
    import torch
    from rich.console import Console
    from rich.progress import Progress, SpinnerColumn, TextColumn
    from rich.panel import Panel
    from rich.table import Table
except ImportError as e:
    print(f"❌ 缺少必要的依赖包: {e}")
    print("请先运行: pip install -r requirements.txt")
    sys.exit(1)

# 初始化控制台
console = Console()


class ModelDownloader:
    """模型下载器"""
    
    def __init__(self, cache_dir: Optional[str] = None):
        """
        初始化模型下载器
        
        Args:
            cache_dir: 模型缓存目录，如果为 None 则使用默认目录
        """
        if cache_dir is None:
            self.cache_dir = project_root / "cache" / "models"
        else:
            self.cache_dir = Path(cache_dir)
        
        # 确保缓存目录存在
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # 模型配置
        self.model_name = "thenlper/gte-large"
        self.model_path = self.cache_dir / "gte-large"
        
    def check_system_requirements(self) -> bool:
        """检查系统要求"""
        console.print("\n🔍 检查系统要求...", style="blue")
        
        # 检查 Python 版本
        python_version = sys.version_info
        if python_version < (3, 8):
            console.print("❌ Python 版本需要 >= 3.8", style="red")
            return False
        
        console.print(f"✅ Python 版本: {python_version.major}.{python_version.minor}.{python_version.micro}", style="green")
        
        # 检查 PyTorch
        try:
            torch_version = torch.__version__
            console.print(f"✅ PyTorch 版本: {torch_version}", style="green")
            
            # 检查 CUDA 可用性
            if torch.cuda.is_available():
                cuda_version = torch.version.cuda
                gpu_count = torch.cuda.device_count()
                console.print(f"✅ CUDA 可用: {cuda_version}, GPU 数量: {gpu_count}", style="green")
            else:
                console.print("ℹ️  CUDA 不可用，将使用 CPU", style="yellow")
                
        except Exception as e:
            console.print(f"❌ PyTorch 检查失败: {e}", style="red")
            return False
        
        # 检查磁盘空间（gte-large 大约需要 1.5GB）
        try:
            import shutil
            free_space = shutil.disk_usage(self.cache_dir).free / (1024**3)  # GB
            if free_space < 2.0:
                console.print(f"❌ 磁盘空间不足: {free_space:.1f}GB 可用，需要至少 2GB", style="red")
                return False
            console.print(f"✅ 磁盘空间充足: {free_space:.1f}GB 可用", style="green")
        except Exception as e:
            console.print(f"⚠️  无法检查磁盘空间: {e}", style="yellow")
        
        return True
    
    def is_model_downloaded(self) -> bool:
        """检查模型是否已下载"""
        required_files = [
            "config.json",
            "pytorch_model.bin",
            "tokenizer.json",
            "tokenizer_config.json"
        ]
        
        if not self.model_path.exists():
            return False
        
        for file_name in required_files:
            if not (self.model_path / file_name).exists():
                return False
        
        return True
    
    def download_with_transformers(self) -> bool:
        """使用 transformers 库下载模型"""
        try:
            console.print(f"\n📥 使用 transformers 下载模型到: {self.model_path}", style="blue")
            
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console
            ) as progress:
                # 下载 tokenizer
                task1 = progress.add_task("下载 tokenizer...", total=None)
                tokenizer = AutoTokenizer.from_pretrained(
                    self.model_name,
                    cache_dir=str(self.model_path),
                    trust_remote_code=True
                )
                progress.update(task1, completed=True)
                
                # 下载模型
                task2 = progress.add_task("下载模型权重...", total=None)
                model = AutoModel.from_pretrained(
                    self.model_name,
                    cache_dir=str(self.model_path),
                    trust_remote_code=True
                )
                progress.update(task2, completed=True)
            
            console.print("✅ transformers 下载完成", style="green")
            return True
            
        except Exception as e:
            console.print(f"❌ transformers 下载失败: {e}", style="red")
            return False
    
    def download_with_sentence_transformers(self) -> bool:
        """使用 sentence-transformers 库下载模型"""
        try:
            console.print(f"\n📥 使用 sentence-transformers 下载模型...", style="blue")
            
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console
            ) as progress:
                task = progress.add_task("下载 sentence-transformers 模型...", total=None)
                
                # 设置缓存目录
                os.environ['SENTENCE_TRANSFORMERS_HOME'] = str(self.cache_dir)
                
                model = SentenceTransformer(self.model_name)
                progress.update(task, completed=True)
            
            console.print("✅ sentence-transformers 下载完成", style="green")
            return True
            
        except Exception as e:
            console.print(f"❌ sentence-transformers 下载失败: {e}", style="red")
            return False
    
    def test_model(self) -> bool:
        """测试模型是否正常工作"""
        try:
            console.print("\n🧪 测试模型...", style="blue")
            
            # 设置缓存目录
            os.environ['SENTENCE_TRANSFORMERS_HOME'] = str(self.cache_dir)
            
            # 加载模型
            model = SentenceTransformer(self.model_name)
            
            # 测试文本
            test_texts = [
                "这是一个测试句子。",
                "LangChain is a framework for building applications with LLMs.",
                "人工智能技术正在快速发展。"
            ]
            
            # 生成嵌入
            embeddings = model.encode(test_texts)
            
            # 验证结果
            if embeddings.shape[0] == len(test_texts) and embeddings.shape[1] > 0:
                console.print(f"✅ 模型测试成功！嵌入维度: {embeddings.shape[1]}", style="green")
                
                # 显示测试结果
                table = Table(title="模型测试结果")
                table.add_column("文本", style="cyan")
                table.add_column("嵌入维度", style="magenta")
                table.add_column("嵌入范围", style="green")
                
                for i, text in enumerate(test_texts):
                    emb = embeddings[i]
                    table.add_row(
                        text[:30] + "..." if len(text) > 30 else text,
                        str(emb.shape[0]),
                        f"[{emb.min():.3f}, {emb.max():.3f}]"
                    )
                
                console.print(table)
                return True
            else:
                console.print("❌ 模型测试失败：嵌入结果异常", style="red")
                return False
                
        except Exception as e:
            console.print(f"❌ 模型测试失败: {e}", style="red")
            return False
    
    def show_model_info(self):
        """显示模型信息"""
        info_panel = Panel.fit(
            f"""
[bold blue]gte-large 模型信息[/bold blue]

[bold]模型名称:[/bold] {self.model_name}
[bold]模型类型:[/bold] 文本嵌入模型
[bold]嵌入维度:[/bold] 1024
[bold]最大序列长度:[/bold] 512
[bold]支持语言:[/bold] 中文、英文
[bold]模型大小:[/bold] ~1.5GB
[bold]缓存位置:[/bold] {self.model_path}

[bold green]特性:[/bold green]
• 高质量的文本嵌入表示
• 支持中英文混合文本
• 适用于语义搜索和相似度计算
• 在多个基准测试中表现优异
            """,
            title="📋 模型详情",
            border_style="blue"
        )
        console.print(info_panel)
    
    def run(self) -> bool:
        """运行下载流程"""
        console.print(Panel.fit(
            "[bold blue]🤖 gte-large 模型下载器[/bold blue]\n"
            "正在为 Deep Risk RAG 系统下载嵌入模型...",
            title="模型下载",
            border_style="blue"
        ))
        
        # 显示模型信息
        self.show_model_info()
        
        # 检查系统要求
        if not self.check_system_requirements():
            return False
        
        # 检查模型是否已存在
        if self.is_model_downloaded():
            console.print("\n✅ 模型已存在，跳过下载", style="green")
            return self.test_model()
        
        # 尝试下载模型
        console.print(f"\n📁 模型将保存到: {self.model_path}", style="blue")
        
        # 优先使用 sentence-transformers
        if self.download_with_sentence_transformers():
            return self.test_model()
        
        # 备用方案：使用 transformers
        console.print("\n🔄 尝试备用下载方案...", style="yellow")
        if self.download_with_transformers():
            return self.test_model()
        
        console.print("\n❌ 所有下载方案都失败了", style="red")
        return False


def main():
    """主函数"""
    try:
        # 创建下载器
        downloader = ModelDownloader()
        
        # 运行下载
        success = downloader.run()
        
        if success:
            console.print("\n🎉 模型下载和测试完成！", style="bold green")
            console.print("现在可以运行主程序: python main.py", style="blue")
        else:
            console.print("\n💥 模型下载失败", style="bold red")
            sys.exit(1)
            
    except KeyboardInterrupt:
        console.print("\n\n⏹️  下载被用户中断", style="yellow")
        sys.exit(1)
    except Exception as e:
        console.print(f"\n💥 发生未预期的错误: {e}", style="bold red")
        sys.exit(1)


if __name__ == "__main__":
    main()
