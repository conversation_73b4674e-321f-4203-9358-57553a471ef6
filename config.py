"""
配置文件 - Deep Risk RAG 系统
"""
import os
from pathlib import Path
from typing import Optional
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """系统配置类"""
    
    # 项目路径
    PROJECT_ROOT: Path = Path(__file__).parent
    DATA_DIR: Path = PROJECT_ROOT / "data"
    DOCUMENTS_DIR: Path = DATA_DIR / "documents"
    CACHE_DIR: Path = PROJECT_ROOT / "cache"
    MODELS_CACHE_DIR: Path = CACHE_DIR / "models"
    
    # DeepSeek API 配置
    DEEPSEEK_API_KEY: Optional[str] = None
    DEEPSEEK_BASE_URL: str = "https://api.deepseek.com"
    DEEPSEEK_MODEL: str = "deepseek-chat"
    
    # 嵌入模型配置
    EMBEDDING_MODEL_NAME: str = "thenlper/gte-large"
    EMBEDDING_MODEL_CACHE_DIR: Optional[str] = None
    EMBEDDING_BATCH_SIZE: int = 32
    EMBEDDING_MAX_LENGTH: int = 512
    
    # 向量数据库配置
    VECTOR_DB_TYPE: str = "chroma"  # chroma, faiss
    CHROMA_PERSIST_DIR: str = "./cache/chroma_db"
    CHROMA_COLLECTION_NAME: str = "deep_risk_documents"
    
    # 文档处理配置
    CHUNK_SIZE: int = 1000
    CHUNK_OVERLAP: int = 200
    MAX_DOCUMENT_SIZE_MB: int = 50
    
    # 检索配置
    RETRIEVAL_TOP_K: int = 5
    SIMILARITY_THRESHOLD: float = 0.7
    
    # RAG 配置
    MAX_CONTEXT_LENGTH: int = 4000
    TEMPERATURE: float = 0.1
    MAX_TOKENS: int = 2000
    
    # 系统配置
    LOG_LEVEL: str = "INFO"
    ENABLE_CACHE: bool = True
    CACHE_TTL_SECONDS: int = 3600
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True


# 全局配置实例
settings = Settings()

# 确保必要的目录存在
def ensure_directories():
    """确保所有必要的目录存在"""
    directories = [
        settings.DATA_DIR,
        settings.DOCUMENTS_DIR,
        settings.CACHE_DIR,
        settings.MODELS_CACHE_DIR,
        Path(settings.CHROMA_PERSIST_DIR).parent,
    ]
    
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)
        
    print(f"✅ 项目目录结构已创建")
    print(f"📁 数据目录: {settings.DOCUMENTS_DIR}")
    print(f"📁 缓存目录: {settings.CACHE_DIR}")
    print(f"📁 模型缓存: {settings.MODELS_CACHE_DIR}")


if __name__ == "__main__":
    ensure_directories()
